import React, { useEffect } from 'react';
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Copy from '../../Copy/Copy';
import './PatientCareSection.css';

const PatientCareSection = () => {
    useEffect(() => {
        gsap.registerPlugin(ScrollTrigger);

        let scrollTriggerInstances = [];

        const initIconAnimations = () => {
            // Kill existing ScrollTrigger instances
            scrollTriggerInstances.forEach(st => st.kill());
            scrollTriggerInstances = [];

            // Set initial states for non-text elements
            gsap.set(".patient-care-profile-icon", { scale: 0, opacity: 0 });
            gsap.set(".patient-care-arrow-icon", { scale: 0, opacity: 0 });
            gsap.set(".trust-indicators .trust-badge", { y: 50, opacity: 0 });

            // Profile Icon Animation
            const profileIconST = ScrollTrigger.create({
                trigger: ".patient-care-profile-icon",
                start: "top 80%",
                end: "bottom 20%",
                animation: gsap.to(".patient-care-profile-icon", {
                    scale: 1,
                    opacity: 1,
                    duration: 1.2,
                    ease: "back.out(1.7)",
                }),
                toggleActions: "play none none reverse",
            });
            scrollTriggerInstances.push(profileIconST);

            // Arrow Icon Animation
            const arrowIconST = ScrollTrigger.create({
                trigger: ".patient-care-arrow-icon",
                start: "top 75%",
                end: "bottom 25%",
                animation: gsap.to(".patient-care-arrow-icon", {
                    scale: 1,
                    opacity: 1,
                    duration: 1,
                    ease: "elastic.out(1, 0.5)",
                }),
                toggleActions: "play none none reverse",
            });
            scrollTriggerInstances.push(arrowIconST);

            // Trust Badges Animation
            const trustBadgesST = ScrollTrigger.create({
                trigger: ".trust-indicators",
                start: "top 80%",
                end: "bottom 20%",
                animation: gsap.to(".trust-indicators .trust-badge", {
                    y: 0,
                    opacity: 1,
                    duration: 0.8,
                    ease: "power3.out",
                    stagger: 0.2,
                }),
                toggleActions: "play none none reverse",
            });
            scrollTriggerInstances.push(trustBadgesST);
        };

        // Initialize animations after a short delay to ensure DOM is ready
        const timer = setTimeout(initIconAnimations, 100);

        // Handle resize
        const handleResize = () => {
            ScrollTrigger.refresh();
        };

        window.addEventListener("resize", handleResize);

        return () => {
            clearTimeout(timer);
            scrollTriggerInstances.forEach(st => st.kill());
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    return (
        <section className="patient-care-header">
            <div className="patient-care-header-content">
                <div className="patient-care-profile-icon">
                    <div className="healthcare-icon">
                        <img src="./Logo/Logo.png" alt="AIR HS Logo" className="logo-image" />
                    </div>
                </div>
                <Copy animateOnScroll={true} delay={0.2}>
                    <p>Trusted by 500+ hospitals worldwide</p>
                </Copy>
                <div className="patient-care-title">
                    <Copy animateOnScroll={true} delay={0.4}>
                        <h1>WHERE AI MEETS</h1>
                        <h1>PATIENT CARE</h1>
                    </Copy>
                </div>
                <div className="patient-care-arrow-icon">
                    <h1>&#8595;</h1>
                </div>
            </div>
            <div className="patient-care-footer">
                <div className="patient-care-footer-symbols">
                    <div className="trust-indicators " >
                        <span className="trust-badge" style={{ color: "#f7f7f2" }}>FDA-compliant</span>
                        <span className="trust-badge" style={{ color: "#f7f7f2" }}>99.9% uptime</span>
                    </div>
                </div>
                <div className="patient-care-footer-scroll-down">
                    <Copy animateOnScroll={true} delay={0.3}>
                        <p className="mn">Smart Healthcare Solutions</p>
                    </Copy>
                </div>
                <div className="patient-care-footer-tags">
                    <Copy animateOnScroll={true} delay={0.3}>
                        <p className="mn">AI Healthcare / 2025</p>
                    </Copy>
                </div>
            </div>
        </section>
    );
};

export default PatientCareSection;
