/* Rader Font Family */
@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-ThinItalic.ttf') format('truetype');
  font-weight: 100;
  font-style: italic;
}

@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-Hairline.ttf') format('truetype');
  font-weight: 50;
  font-style: normal;
}

@font-face {
  font-family: 'Rader';
  src: url('/rader/PPRader-HairlineItalic.ttf') format('truetype');
  font-weight: 50;
  font-style: italic;
}

/* Global font application */
* {
  font-family: 'Rader', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: visible;
}

#root {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

.App {
  min-height: 100vh;
  width: 100vw;
  min-width: 100vw;
  background-image: url('/Bg/image.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  position: relative;
  margin: 0;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* App.css - Main application styles */