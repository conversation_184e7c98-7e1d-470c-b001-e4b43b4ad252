"use client";
import "./ProcessCards.css";

import { useGSAP } from "@gsap/react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useRef, useState } from "react";
import Copy from "../../Copy/Copy";

gsap.registerPlugin(ScrollTrigger);

const ProcessCards = () => {
  const containerRef = useRef(null);
  const [videoErrors, setVideoErrors] = useState({});

  const handleVideoError = (index) => {
    setVideoErrors(prev => ({
      ...prev,
      [index]: true
    }));
  };

  const processCardsData = [
    {
      index: "01",
      title: "Smart Scheduling",
      video: "/Solution/s1.mp4", // Replace with your video file path
      image: "/Solution/img5.png", // Fallback image
      description:
        "AI-powered scheduling system that automatically optimizes doctor appointments, surgery slots, and resource allocation. Reduces scheduling conflicts by 95% and maximizes hospital efficiency.",
    },
    {
      index: "02",
      title: "Real-Time Analytics",
      video: "/Solution/s3.mp4",
      image: "/Solution/img3.png", // Fallback image
      description:
        "Advanced analytics dashboard providing real-time insights into patient flow, staff performance, and resource utilization. Make data-driven decisions that improve patient outcomes.",
    },
    {
      index: "03",
      title: "Error-Free Billing",
      video: "/Solution/s1.mp4",
      description:
        "Automated billing system with AI verification that eliminates manual errors and ensures accurate insurance claims. Reduces billing errors by 98% and accelerates payment processing.",
    },
    {
      index: "04",
      title: "Patient Care Hub",
      video: "/Solution/s1.mp4", // Changed to s2.mp4 to avoid conflicts
      image: "/Solution/img6.png", // Fallback image
      description:
        "Centralized patient management system that tracks medical history, treatment plans, and care coordination. Improves patient satisfaction and streamlines healthcare delivery.",
    },
  ];

  useGSAP(() => {
    // Only create ScrollTriggers when the container is in view
    if (!containerRef.current) return;

    // Scope queries to this component's container
    const processCards = containerRef.current.querySelectorAll(".process-card");

    // Add a delay to ensure ProcessCards only animate after ProblemSol is done
    const initScrollTrigger = ScrollTrigger.create({
      trigger: containerRef.current,
      start: "top bottom",
      once: true,
      onEnter: () => {
        processCards.forEach((card, index) => {
          if (index < processCards.length - 1) {
            ScrollTrigger.create({
              trigger: card,
              start: "top top",
              endTrigger: processCards[processCards.length - 1],
              end: "top top",
              pin: true,
              pinSpacing: false,
              id: `card-pin-${index}`,
            });
          }

          if (index < processCards.length - 1) {
            ScrollTrigger.create({
              trigger: processCards[index + 1],
              start: "top bottom",
              end: "top top",
              onUpdate: (self) => {
                const progress = self.progress;
                const scale = 1 - progress * 0.25;
                const rotation = (index % 2 === 0 ? 5 : -5) * progress;
                const afterOpacity = progress;

                gsap.set(card, {
                  scale: scale,
                  rotation: rotation,
                  "--after-opacity": afterOpacity,
                });
              },
            });
          }
        });
      }
    });

    return () => {
      if (initScrollTrigger) initScrollTrigger.kill();
    };
  }, []);

  return (
    <>
      {/* Key Features Header Section */}
      <section className="key-features-header">
        <div className="key-features-container">
          <div className="key-features-header-content">
            <Copy animateOnScroll={true}>
              <p className="primary sm">Advanced Healthcare</p>
              <p className="primary sm">Management System</p>
            </Copy>
          </div>

          <div className="key-features-header-content2">
            <Copy animateOnScroll={true}>
              <p className="primary sm">Intelligent Solutions</p>
              <p className="primary sm">for Modern Healthcare</p>
            </Copy>
          </div>

          <Copy animateOnScroll={true}>
            <h1>KEY FEATURES</h1>
            <h1>OF AIR-HS</h1>
          </Copy>

          <div className="key-features-footer-content">
            <Copy animateOnScroll={true}>
              <p className="primary sm">Streamlined Operations</p>
              <p className="primary sm">Enhanced Patient Care</p>
            </Copy>
          </div>

          <div className="key-features-footer-content2">
            <Copy animateOnScroll={true}>
              <p className="primary sm">Digital Transformation</p>
              <p className="primary sm">Healthcare Innovation</p>
            </Copy>
          </div>
        </div>
      </section>

      {/* Process Cards Section */}
      <div ref={containerRef} className="process-cards">
        {processCardsData.map((cardData, index) => (
          <div key={index} className="process-card">
            <div className="process-card-index">
              <h1>{cardData.index}</h1>
            </div>
            <div className="process-card-content">
              <div className="process-card-content-wrapper">
                <h1 className="process-card-header">{cardData.title}</h1>

                <div className="process-card-img">
                  {cardData.video && !videoErrors[index] ? (
                    <video
                      src={cardData.video}
                      autoPlay
                      loop
                      muted
                      playsInline
                      onError={() => handleVideoError(index)}
                      style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                    />
                  ) : (
                    <img src={cardData.image} alt="" />
                  )}
                </div>

                <div className="process-card-copy">
                  <div className="process-card-copy-title">
                    <p className="caps">(Key Feature)</p>
                  </div>
                  <div className="process-card-copy-description">
                    <p>{cardData.description}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default ProcessCards;
