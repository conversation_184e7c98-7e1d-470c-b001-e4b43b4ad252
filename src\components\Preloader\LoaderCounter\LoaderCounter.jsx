"use client";
import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import "./LoaderCounter.css";

const LoaderCounter = ({
  onComplete,
  showProgressBar = true,
  progressBarPosition = "right", // "right" | "left" | "top" | "bottom"
  delay = 0.5,
  countDuration = 2.5,
  staggerDelay = 0.1,
  progressBarWidth = "0.45rem"
}) => {
  const counterRef = useRef(null);
  const progressBarRef = useRef(null);

  // Counter data - you can modify these numbers as needed
  const counterData = [
    [0, 0],
    [2, 7],
    [6, 5],
    [9, 8],
    [9, 9]
  ];

  useEffect(() => {
    const tl = gsap.timeline({
      delay,
      defaults: {
        ease: "power2.out",
      },
    });

    const counts = document.querySelectorAll(".loader-count");
    const progressBar = progressBarRef.current;

    const progressTl = gsap.timeline({
      delay,
    });

    counts.forEach((count, index) => {
      const digits = count.querySelectorAll(".loader-digit h1");

      tl.to(
        digits,
        {
          y: "0%",
          duration: countDuration,
          stagger: staggerDelay,
        },
        index * countDuration
      );

      if (index < counts.length - 1) {
        tl.to(
          digits,
          {
            y: "-120%",
            duration: countDuration,
            stagger: staggerDelay,
          },
          index * countDuration + countDuration
        );
      }

      if (showProgressBar && progressBar) {
        progressTl.to(
          progressBar,
          {
            scaleY: (index + 1) / counts.length,
            duration: countDuration,
            ease: "power2.out",
          },
          index * countDuration
        );
      }
    });

    if (showProgressBar && progressBar) {
      progressTl
        .set(progressBar, {
          transformOrigin: "top",
        })
        .to(progressBar, {
          scaleY: 0,
          duration: 0.75,
          ease: "power2.out", // Changed from "hop" to avoid dependency issues
        })
        .call(() => {
          if (onComplete) {
            onComplete();
          }
        });
    } else {
      // If no progress bar, call onComplete after counter animation
      tl.call(() => {
        if (onComplete) {
          onComplete();
        }
      });
    }

    return () => {
      tl.kill();
      progressTl.kill();
    };
  }, [onComplete, showProgressBar, delay, countDuration, staggerDelay]);

  const getProgressBarStyles = () => {
    const baseStyles = {
      position: "fixed",
      backgroundColor: "#828fd7", //  // Blackish color
      display: "flex",
      transformOrigin: "bottom",
      transform: "scaleY(0%)",
      zIndex: 100,
    };

    switch (progressBarPosition) {
      case "right":
        return {
          ...baseStyles,
          top: 0,
          right: 0,
          width: progressBarWidth,
          height: "100vh",
        };
      case "left":
        return {
          ...baseStyles,
          top: 0,
          left: 0,
          width: progressBarWidth,
          height: "100vh",
        };
      case "top":
        return {
          ...baseStyles,
          top: 0,
          left: 0,
          width: "100vw",
          height: progressBarWidth,
          transformOrigin: "left",
        };
      case "bottom":
        return {
          ...baseStyles,
          bottom: 0,
          left: 0,
          width: "100vw",
          height: progressBarWidth,
          transformOrigin: "left",
        };
      default:
        return {
          ...baseStyles,
          top: 0,
          right: 0,
          width: progressBarWidth,
          height: "100vh",
        };
    }
  };

  return (
    <div className="loader-counter-container">
      {showProgressBar && (
        <div
          ref={progressBarRef}
          className="loader-progress-bar"
          style={getProgressBarStyles()}
        />
      )}

      <div ref={counterRef} className="loader-counter">
        {counterData.map((countPair, countIndex) => (
          <div key={countIndex} className="loader-count">
            {countPair.map((digit, digitIndex) => (
              <div key={digitIndex} className="loader-digit">
                <h1>{digit}</h1>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default LoaderCounter;
