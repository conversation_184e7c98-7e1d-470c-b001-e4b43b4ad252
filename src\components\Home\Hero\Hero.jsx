import React, { useEffect } from 'react';
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import './Hero.css';

const Hero = () => {
    useEffect(() => {
        gsap.registerPlugin(ScrollTrigger);

        const heroImg = document.querySelector(".hero-img img");
        let currentImageIndex = 1;
        const totalImages = 10;
        let scrollTriggerInstance = null;

        // Image cycling functionality
        const imageInterval = setInterval(() => {
            currentImageIndex =
                currentImageIndex >= totalImages ? 1 : currentImageIndex + 1;
            if (heroImg) {
                heroImg.src = `../hero/img${currentImageIndex}.png`;
            }
        }, 250);

        const initAnimations = () => {
            if (scrollTriggerInstance) {
                scrollTriggerInstance.kill();
            }

            // Vertical scaling animation
            scrollTriggerInstance = ScrollTrigger.create({
                trigger: ".hero-img-holder",
                start: "top bottom",
                end: "top top",
                onUpdate: (self) => {
                    const progress = self.progress;
                    gsap.set(".hero-img", {
                        y: `${-110 + 110 * progress}%`,
                        scale: 0.25 + 0.70 * progress,
                        rotation: -15 + 15 * progress,
                    });
                },
            });
        };

        initAnimations();

        const handleResize = () => {
            initAnimations();
        };

        window.addEventListener("resize", handleResize);

        // Cleanup function
        return () => {
            clearInterval(imageInterval);
            if (scrollTriggerInstance) {
                scrollTriggerInstance.kill();
            }
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    return (
        <>
            {/* home - hero */}
            <section className="hero">
                <div className="hero-header-wrapper">
                    <div className="hero-header hero-header-1">
                        <h1>AI Relay</h1>
                    </div>
                    <div className="hero-header hero-header-2">
                        <h1>Hospital</h1>
                    </div>
                    <div className="hero-header hero-header-3">
                        <h1>System</h1>
                    </div>

                </div>
                <div className="hero-footer">
                    <div className="hero-footer-tags">
                        <p className="mn"> ✦  AI-Powered Healthcare</p>
                        <p className="mn"> ✦  Compassionate Technology</p>
                    </div>
                </div>
                <div className="hero-center-bottom">
                    <p>This is healthcare today. But it doesn't have to be tomorrow.</p>
                    <div className="hero-footer-tags-desktop">
                        <p className="mn"> ✦  AI-Powered Healthcare</p>
                        <p className="mn"> ✦  Compassionate Technology</p>
                    </div>
                </div>
            </section>

            {/* home - hero img holder */}
            <section className="hero-img-holder">
                <div className="hero-img">
                    <img src="/hero/img1.png" alt="" />
                </div>
            </section>


        </>
    );
};

export default Hero;
