import React from 'react';
import './Home.css';
import React<PERSON>enis from "lenis/react";
import Navbar from './Navbar/Navbar';
import Hero from './Hero/Hero.jsx';
import PatientCareSection from './PatientCareSection/PatientCareSection.jsx';
import HorizontalScroll from './HorizontalScroll/HorizontalScroll.jsx';
import ProblemSol from './ProblemSol/ProblemSol.jsx';
import ProcessCards from './KeyFeatures/ProcessCards.jsx';
import FoundersEdge from './FoundersEdge/FoundersEdge.jsx';
import Reviews from './Review/Reviews.jsx';
import ContactForm from '../ContactForm/ContactForm.jsx';
import Footer from '../Footer/Footer.jsx';

const Home = () => {
  return (
    <ReactLenis root>
      <div className="page home-page">
        <Navbar />
        <div id="home">
          <Hero />
        </div>
        <div id="product">
          <PatientCareSection />
          <HorizontalScroll />
        </div>
        <div id="solutions">
          <ProblemSol />
          <ProcessCards />
        </div>
        <div id="about">
          <FoundersEdge />
        </div>
        <div id="reviews">
          <Reviews />
        </div>
        <div id="contact">
          <ContactForm />
          <Footer />
        </div>
      </div>
    </ReactLenis>
  );
};

export default Home;
