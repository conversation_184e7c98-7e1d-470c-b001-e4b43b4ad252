.digit-counter {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
  transform-style: preserve-3d;
}

.counter-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 10px;
}

.digit-wrapper {
  position: relative;
  overflow: hidden;
  height: 60px;
  width: 45px;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.digit-strip {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 600px;
  /* 10 digits × 60px each */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.digit-item {
  font-family: 'Rader', monospace;
  font-size: 70px;
  font-weight: 700;
  color: #666666;
  height: 60px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.counter-suffix {
  font-family: 'Rader', monospace;
  font-size: 28px;
  font-weight: 700;
  color: #434242;
  margin-left: 5px;
}

.counter-label {
  font-family: 'Rader', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #2f2d2d82;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-top: 5px;
}



/* Responsive design */
@media screen and (max-width: 768px) {
  .digit-wrapper {
    height: 45px;
    width: 35px;
  }

  .digit-strip {
    height: 450px;
    /* 10 digits × 45px each */
  }

  .digit-item {
    font-size: 24px;
    height: 45px;
  }

  .counter-suffix {
    font-size: 20px;
  }
}