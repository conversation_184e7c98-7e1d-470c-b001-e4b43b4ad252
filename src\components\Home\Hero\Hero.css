/* CSS Variables for Hero component */
:root {
    --fg: #121214;
    --bg: #f7f7f2;
    --accent1: #727072;
    --accent2: #8a8a8a;
    --accent3: #a0a0a0;
}

/* home - hero */
.hero {
    position: relative;
    width: 100vw;
    height: 100svh;
    padding: 2em;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow-x: hidden;
}

.hero .hero-header h1 {
    font-size: 10vw;
    line-height: 1.2;
    color: var(--fg);
    font-weight: bold;
}

.hero .hero-header.hero-header-1 {
    position: relative;
    transform: translate(-40%, 10%);
    z-index: 1;
}

.hero .hero-header.hero-header-2 {
    position: relative;
    transform: translateX(80%);
    z-index: 2;
}

.hero .hero-header.hero-header-3 {
    position: relative;
    transform: translate(-25%, -20%);
    z-index: 2;
}


.hero .hero-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem 2rem;
    margin: 0 2rem 2rem 2rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #121214;
    height: auto;
    z-index: 2;
}

.hero .hero-footer .hero-footer-scroll-down {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.hero .hero-footer .hero-footer-tags {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: clamp(1rem, 4vw, 3rem);
    flex-wrap: wrap;
}

.hero .hero-footer .hero-footer-tags p {
    font-size: clamp(0.8rem, 1.2vw, 1rem);
    margin: 0;
    white-space: nowrap;
}

.hero .hero-center-bottom {
    position: absolute;
    bottom: clamp(4rem, 8vh, 6rem);
    left: 2%;
    right: 2%;
    z-index: 3;
    border-top: 1px dashed var(--fg);
    padding-top: 1em;
    margin: 0 clamp(1rem, 4vw, 4rem);
    /* Left and right gaps for the border */
}

.hero .hero-center-bottom p {
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    color: var(--fg);
    font-weight: 500;
    margin: 0;

}

/* home - hero img holder */
.hero-img-holder {
    position: relative;
    width: 100vw;
    height: 100svh;
    padding: 2em;
}

.hero-img-holder .hero-img {
    position: relative;
    width: 100%;
    height: 100%;
    transform: translateY(-110%) scale(0.25) rotate(-15deg);
    border: 0.3em solid var(--fg);
    border-radius: 2em;
    overflow: hidden;
}

.hero-img-holder .hero-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
}



/* home - featured work */
.featured-work {
    position: relative;
    width: 100vw;
    height: 100svh;
    overflow: hidden;
}

.featured-work .featured-titles {
    position: relative;
    width: 500vw;
    height: 100vh;
    display: flex;
    will-change: transform;
}

.featured-work .featured-title-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.featured-title-img {
    position: relative;
    top: 0px;
    width: calc(100% - 4em);
    height: 150px;
    border: 0.2em solid var(--fg);
    border-radius: 1em;
    overflow: hidden;
    display: none;
}

.featured-work .featured-title-wrapper h1 {
    text-align: center;
    transform: translateY(-0.5em);
}

.featured-work .featured-images {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200vw;
    height: 200vh;
    transform-style: preserve-3d;
    perspective: 500px;
}

.featured-work .featured-img-card {
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 2em;
    overflow: hidden;
}

.featured-work .featured-work-indicator {
    position: absolute;
    top: 50%;
    right: 2em;
    transform: translate(0%, -50%);
    width: 2rem;
    height: max-content;
    padding: 1.25rem 0.65rem;
    background-color: var(--fg);
    color: var(--bg);
    border-radius: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.35rem;
    z-index: 10;
}

.featured-work .featured-work-indicator .indicator {
    width: 100%;
    height: 1.5px;
    background-color: var(--bg);
    opacity: 0.2;
}

.featured-work .featured-work-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 2em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 2;
}

/* home - services header */
.services-header {
    position: relative;
    width: 100vw;
    height: 100vh;
    padding: 2em;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.services-header .services-profile-icon {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 1em;
    margin-bottom: 2em;
    outline: 0.25rem solid var(--accent3);
    border: 0.25rem solid var(--fg);
    overflow: hidden;
}

.services-header .services-header-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1em;
}

.services-header .services-header-title {
    margin-bottom: 6em;
}

/* home - services */
.services .service-card {
    position: relative;
    min-height: 300px;
}

.services .service-card-inner {
    position: relative;
    will-change: transform;
    width: calc(100vw - 4em);
    height: 100%;
    margin: 0 auto;
    padding: 2em;
    display: flex;
    gap: 4em;
    border-radius: 2em;
    min-height: 500px;
}

.services .service-card-content {
    flex: 3;
    display: flex;
    flex-direction: column;
    gap: 2em;
}

.services .service-card-img {
    flex: 1;
    aspect-ratio: 4/5;
    border-radius: 2em;
    overflow: hidden;
}

.services #service-card-1 .service-card-inner {
    background-color: var(--accent1);
}

.services #service-card-2 .service-card-inner {
    background-color: var(--accent2);
}

.services #service-card-3 .service-card-inner {
    background-color: var(--accent3);
}

.services #service-card-4 .service-card-inner {
    background-color: var(--fg);
    color: var(--bg);
}

/* Large Desktop Layout - Horizontal Alignment */
@media (min-width: 1201px) {

    /* Hide the original footer on desktop */
    .hero .hero-footer {
        display: none;
    }

    /* Style the center-bottom section for horizontal layout */
    .hero .hero-center-bottom {
        position: absolute;
        bottom: clamp(4rem, 8vh, 6rem);
        left: clamp(1rem, 4vw, 4rem);
        right: clamp(1rem, 4vw, 4rem);
        z-index: 3;
        border-top: 1px dashed var(--fg);
        padding-top: 1em;
        margin: 0;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
    }

    .hero .hero-center-bottom p {
        margin: 0;
        font-size: clamp(1rem, 2.5vw, 1.5rem);
        color: var(--fg);
        font-weight: 500;
    }

    /* Show and style desktop footer tags */
    .hero .hero-footer-tags-desktop {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: clamp(1rem, 4vw, 3rem);
        flex-wrap: nowrap;
    }

    .hero .hero-footer-tags-desktop p {
        font-size: clamp(0.8rem, 1.2vw, 1rem);
        margin: 0;
        white-space: nowrap;
        color: var(--fg);
    }
}

/* Hide desktop tags on smaller screens */
@media (max-width: 1200px) {
    .hero .hero-footer-tags-desktop {
        display: none;
    }

    .hero .hero-footer {
        margin: 0 1.5rem 1.5rem 1.5rem;
        padding: 1rem 1.5rem;
    }

    .hero .hero-footer .hero-footer-tags {
        gap: clamp(0.8rem, 3vw, 2rem);
    }

    /* Restore original center-bottom layout for smaller screens */
    .hero .hero-center-bottom {
        display: block;
    }
}

@media (max-width: 1000px) {
    .hero .hero-footer .hero-footer-symbols {
        display: none;
    }

    .hero .hero-footer {
        margin: 0 1rem 1rem 1rem;
        padding: 1rem;
        justify-content: center;
    }

    .hero .hero-footer .hero-footer-tags {
        gap: clamp(0.6rem, 2.5vw, 1.5rem);
        justify-content: center;
    }

    .hero .hero-footer .hero-footer-scroll-down {
        left: 2em;
        transform: translateX(0%);
    }

    .featured-work {
        padding: 4em 0;
    }

    .featured-work,
    .featured-work .featured-titles {
        height: max-content;
    }

    .featured-work .featured-title-wrapper {
        gap: 1em;
    }

    .featured-work .featured-title-wrapper:nth-child(1) {
        margin-bottom: 2em;
    }

    .featured-work .featured-title-wrapper h1 {
        transform: translateY(0);
        width: 75%;
    }

    .featured-work .featured-work-footer {
        position: relative;
        margin-top: 4em;
        justify-content: center;
    }

    .featured-work .featured-work-footer p:nth-child(1),
    .featured-work .featured-work-footer p:nth-child(2) {
        display: none;
    }

    .featured-work .featured-work-indicator {
        display: none;
    }

    .featured-work .featured-images {
        display: none;
    }

    .featured-title-img {
        display: block;
    }

    .featured-work .featured-titles {
        width: 100vw;
        flex-direction: column;
        gap: 2em;
    }

    .services-header {
        height: max-content;
    }

    .services {
        display: flex;
        flex-direction: column;
        gap: 2em;
    }

    .services .service-card-inner {
        min-height: 0;
        flex-direction: column;
        justify-content: center;
        gap: 1em;
        text-align: center;
        border: 0.2em solid var(--fg);
        border-radius: 1em;
    }

    .services .service-card-img {
        aspect-ratio: 5/3;
        border: 0.2em solid var(--fg);
        border-radius: 1em;
    }
}

@media (max-width: 768px) {
    .hero .hero-footer {
        margin: 0 0.5rem 0.5rem 0.5rem;
        padding: 0.8rem;
        bottom: clamp(10rem, 14vh, 12rem);
        /* Position above the dashed line with space */
        justify-content: center;
    }

    .hero .hero-footer .hero-footer-tags {
        gap: clamp(1rem, 3vw, 2rem);
        flex-direction: row;
        /* Keep tags in one line */
        align-items: center;
        justify-content: center;
    }

    .hero .hero-footer .hero-footer-tags p {
        font-size: clamp(0.7rem, 1vw, 0.9rem);
        text-align: center;
        white-space: nowrap;
    }

    .hero .hero-center-bottom {
        bottom: clamp(3rem, 6vh, 4.5rem);
        margin: 0 clamp(0.5rem, 2vw, 2rem);
        text-align: center;
        /* Center the text below dashed line */
    }
}

@media (max-width: 480px) {
    .hero .hero-footer {
        margin: 0 0.25rem 0.25rem 0.25rem;
        padding: 0.6rem;
        bottom: clamp(7rem, 10vh, 8.5rem);
        /* Maintain spacing on smaller screens */
    }

    .hero .hero-footer .hero-footer-tags {
        gap: 0.4rem;
    }

    .hero .hero-footer .hero-footer-tags p {
        font-size: 0.75rem;
    }

    .hero .hero-center-bottom {
        bottom: clamp(2.5rem, 5vh, 3.5rem);
        margin: 0 0.5rem;
    }
}

@media (max-width: 320px) {
    .hero .hero-footer {
        margin: 0;
        padding: 0.5rem;
        bottom: 6.5rem;
        /* Ensure adequate spacing on very small screens */
    }

    .hero .hero-footer .hero-footer-tags p {
        font-size: 0.7rem;
    }

    .hero .hero-center-bottom {
        bottom: 2rem;
        margin: 0 0.25rem;
    }
}