import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import LoaderCounter from './LoaderCounter/LoaderCounter';
import './Preloader.css';

const Preloader = ({ onComplete }) => {
  const [showLoader, setShowLoader] = useState(true);
  const [loaderStarted, setLoaderStarted] = useState(false);
  const airRef = useRef(null);
  const hsRef = useRef(null);
  const titleContainerRef = useRef(null);

  const handleLoaderComplete = () => {
    setShowLoader(false);

    // Animate text back to center
    gsap.to([airRef.current, hsRef.current], {
      x: 0,
      duration: 1,
      ease: "power2.inOut",
      onComplete: () => {
        // After text animation, call the parent's onComplete
        if (onComplete) {
          onComplete();
        }
      }
    });
  };

  const startLoader = () => {
    setLoaderStarted(true);
    // Split animation: move AIR to left and HS to right with balanced values
    gsap.to(airRef.current, {
      x: -200,
      duration: 1,
      ease: "power2.inOut"
    });
    gsap.to(hsRef.current, {
      x: 200,
      duration: 1,
      ease: "power2.inOut"
    });
  };

  useEffect(() => {
    // Start the split animation after a short delay
    const timer = setTimeout(() => {
      startLoader();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="preloader">
      <div className="preloader-content">
        <div className="preloader-header">
          <div className="title-container" ref={titleContainerRef}>
            <h1 className="preloader-title">
              <span ref={airRef} className="text-part">AIR</span>
              {showLoader && loaderStarted && (
                <LoaderCounter
                  onComplete={handleLoaderComplete}
                  showProgressBar={true}
                  progressBarPosition="center"
                  delay={0.5}
                  countDuration={2.5}
                  staggerDelay={0.1}
                />
              )}
              <span ref={hsRef} className="text-part">HS</span>
            </h1>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Preloader;
