import React, { useEffect, useRef } from "react";
import { <PERSON> } from "react-router-dom";
import Copy from "../../Copy/Copy";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "./ProblemSol.css";

gsap.registerPlugin(ScrollTrigger);

const ProblemSol = () => {
    const stickyTitlesRef = useRef(null);
    const titlesRef = useRef([]);
    const stickyWorkHeaderRef = useRef(null);
    const homeWorkRef = useRef(null);
    const backgroundRef = useRef(null);

    // Healthcare solutions data
    const solutionItems = [
        {
            id: 1,
            title: "SMART PRESCRIPTION MANAGEMENT",
            subtitle: "MEDICATION AUTOMATION",
            number: "01 - 03",
            image: "/Solution/img6.png"
        },
        {
            id: 2,
            title: "COMPLETE HOSPITAL AUTOMATION",
            subtitle: "WORKFLOW OPTIMIZATION",
            number: "02 - 03",
            image: "/Solution/img5.png"
        },
        {
            id: 3,
            title: "PATIENT FLOW OPTIMIZATION",
            subtitle: "REAL-TIME TRACKING",
            number: "03 - 03",
            image: "/Solution/img3.png"
        }
    ];

    useEffect(() => {
        const handleResize = () => {
            ScrollTrigger.refresh();
        };

        window.addEventListener("resize", handleResize);

        const stickySection = stickyTitlesRef.current;
        const titles = titlesRef.current.filter(Boolean);
        const background = backgroundRef.current;

        if (!stickySection || titles.length !== 3 || !background) {
            window.removeEventListener("resize", handleResize);
            return;
        }

        // Initial setup for titles
        gsap.set(titles[0], { opacity: 1, scale: 1 });
        gsap.set(titles[1], { opacity: 0, scale: 0.75 });
        gsap.set(titles[2], { opacity: 0, scale: 0.75 });

        // Initial background setup - only show when section is in view
        gsap.set(background, {
            backgroundColor: "#121214",
            opacity: 0
        });

        // Show background when section comes into view
        gsap.to(background, {
            opacity: 1,
            duration: 0.3,
            scrollTrigger: {
                trigger: stickySection,
                start: "top bottom",
                end: "top top",
                toggleActions: "play none none reverse"
            }
        });

        // Pin the sticky titles section
        const pinTrigger = ScrollTrigger.create({
            trigger: stickySection,
            start: "top top",
            end: `+=${window.innerHeight * 6}`,
            pin: true,
            pinSpacing: true,
        });

        // Animation timeline for title transitions and background color changes
        const masterTimeline = gsap.timeline({
            scrollTrigger: {
                trigger: stickySection,
                start: "top top",
                end: `+=${window.innerHeight * 6}`,
                scrub: 0.5,
            },
        });

        // First title fade out, second title fade in (still problem phase)
        masterTimeline
            .to(
                titles[0],
                {
                    opacity: 0,
                    scale: 0.75,
                    duration: 0.3,
                    ease: "power2.out",
                },
                1
            )
            .to(
                titles[1],
                {
                    opacity: 1,
                    scale: 1,
                    duration: 0.3,
                    ease: "power2.in",
                },
                1.25
            );

        // Second title fade out, third title fade in + background transition to white (solution phase)
        masterTimeline
            .to(
                titles[1],
                {
                    opacity: 0,
                    scale: 0.75,
                    duration: 0.3,
                    ease: "power2.out",
                },
                2.5
            )
            .to(
                titles[2],
                {
                    opacity: 1,
                    scale: 1,
                    duration: 0.3,
                    ease: "power2.in",
                },
                2.75
            )
            .to(
                background,
                {
                    backgroundColor: "#f7f7f2",
                    duration: 0.5,
                    ease: "power2.inOut",
                },
                2.6
            )
            .to(
                ".sticky-titles .primary.sm",
                {
                    color: "#121214",
                    duration: 0.5,
                    ease: "power2.inOut",
                },
                2.6
            )
            // Clear background at the end to transition to ProcessCards
            .to(
                background,
                {
                    backgroundColor: "transparent",
                    opacity: 0,
                    duration: 0.3,
                    ease: "power2.inOut",
                },
                3.8
            );



        // Cleanup function
        return () => {
            pinTrigger.kill();
            if (masterTimeline.scrollTrigger) {
                masterTimeline.scrollTrigger.kill();
            }
            masterTimeline.kill();
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    // Dedicated useEffect for pinning the background text
    useEffect(() => {
        const workHeaderSection = stickyWorkHeaderRef.current;
        const homeWorkSection = homeWorkRef.current;

        if (!workHeaderSection || !homeWorkSection) return;

        // Small delay to ensure DOM is ready
        const timer = setTimeout(() => {
            const workHeaderPinTrigger = ScrollTrigger.create({
                trigger: workHeaderSection,
                start: "top top",
                endTrigger: homeWorkSection,
                end: "bottom bottom",
                pin: true,
                pinSpacing: false,
                markers: false, // Set to true for debugging
            });

            return () => {
                if (workHeaderPinTrigger) {
                    workHeaderPinTrigger.kill();
                }
            };
        }, 100);

        return () => {
            clearTimeout(timer);
        };
    }, []);

    return (
        <div className="problem-sol-container">
            {/* Background element for color transitions */}
            <div ref={backgroundRef} className="problem-sol-background"></div>

            {/* Sticky Titles Section */}
            <section ref={stickyTitlesRef} className="sticky-titles">
                <div className="sticky-titles-nav">
                    <p className="primary sm">Problem & Solution</p>
                    <p className="primary sm">Healthcare Revolution</p>
                </div>
                <div className="sticky-titles-footer">
                    <p className="primary sm">AI-Powered Healthcare</p>
                    <p className="primary sm">Compassionate Technology</p>
                </div>
                <h2 ref={(el) => (titlesRef.current[0] = el)} className="problem-title">
                    🚨 Hospitals lose ₹200Cr annually due to manual errors, inefficient scheduling, and billing mistakes.
                </h2>
                <h2 ref={(el) => (titlesRef.current[1] = el)} className="problem-title">
                    Staff burnout increases by 40% while patient satisfaction drops due to endless paperwork and delays.
                </h2>
                <h2 ref={(el) => (titlesRef.current[2] = el)} className="solution-title">
                    🤖 AIR-HS automates scheduling & billing, reducing errors by 95% and saving hospitals millions.
                </h2>
            </section>

            {/* Sticky Work Header Section - Background Text */}
            <section ref={stickyWorkHeaderRef} className="sticky-work-header">
                <Copy animateOnScroll={true}>
                    <h1>AIR-HS SOLUTIONS</h1>
                </Copy>
            </section>

            {/* Solutions Section - Scrolling Content */}
            <section ref={homeWorkRef} className="solutions-section">
                <div className="solutions-list">
                    {solutionItems.map((solution, index) => (
                        <div key={solution.id} className="solution-item">
                            <div className="solution-number">
                                <Copy animateOnScroll={true}>
                                    <p>{solution.number}</p>
                                </Copy>
                            </div>
                            <div className="solution-content">
                                <Copy animateOnScroll={true}>
                                    <h2>{solution.title}</h2>
                                </Copy>
                                <div className="solution-image">
                                    <img src={solution.image} alt={solution.title} />
                                </div>
                                <Copy animateOnScroll={true}>
                                    <p className="solution-subtitle">{solution.subtitle}</p>
                                </Copy>
                            </div>
                        </div>
                    ))}
                </div>
            </section>
        </div>
    );
};

export default ProblemSol;