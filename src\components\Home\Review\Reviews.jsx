import reviews from "./reviews";
import React, { useState, useEffect, useRef } from "react";
import "./Reviews.css";

import SplitType from "split-type";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";

import { BiSolidQuoteLeft } from "react-icons/bi";

gsap.registerPlugin(ScrollTrigger, useGSAP);

const Reviews = () => {
    const [activeReview, setActiveReview] = useState(0);
    const reviewsContainerRef = useRef(null);
    const trustedHospitalsRef = useRef(null);
    const initialRenderRef = useRef(true);
    const animationInProgressRef = useRef(false);
    const hasInitialClickRef = useRef(false);

    // Hospital data
    const hospitalsData = [
        {
            id: 1,
            name: "APOLLO HOSPITALS",
            image: "https://images.unsplash.com/photo-1586773860418-d37222d8fce3?q=80&w=2073&auto=format&fit=crop"
        },
        {
            id: 2,
            name: "FORTIS HEALTHCARE",
            image: "https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?q=80&w=2053&auto=format&fit=crop"
        },
        {
            id: 3,
            name: "MAX HEALTHCARE",
            image: "https://images.unsplash.com/photo-**********-a9333d879b1f?q=80&w=2070&auto=format&fit=crop"
        },
        {
            id: 4,
            name: "AIIMS DELHI",
            image: "https://images.unsplash.com/photo-1538108149393-fbbd81895907?q=80&w=2128&auto=format&fit=crop"
        }
    ];

    // GSAP animation for trusted hospitals
    useEffect(() => {
        if (!trustedHospitalsRef.current) return;

        // Simple animation without SplitType for now
        const hospitalBoxes = trustedHospitalsRef.current.querySelectorAll('.hospital');
        const hospitalNames = trustedHospitalsRef.current.querySelectorAll('.hospital-name');

        // Set initial state
        gsap.set(hospitalBoxes, { y: 50, opacity: 0 });
        gsap.set(hospitalNames, { y: 20, opacity: 0 });

        // Create scroll trigger animation
        ScrollTrigger.create({
            trigger: trustedHospitalsRef.current,
            start: "top 80%",
            onEnter: () => {
                // Animate hospital boxes first
                gsap.to(hospitalBoxes, {
                    y: 0,
                    opacity: 1,
                    duration: 0.8,
                    stagger: 0.1,
                    ease: "power4.out",
                });

                // Then animate text
                gsap.to(hospitalNames, {
                    y: 0,
                    opacity: 1,
                    duration: 0.6,
                    stagger: 0.1,
                    ease: "power4.out",
                    delay: 0.2,
                });
            }
        });

        // Add hover effect for background images
        hospitalBoxes.forEach((box, index) => {
            const imageUrl = hospitalsData[index].image;

            box.addEventListener('mouseenter', () => {
                box.style.setProperty('--bg-image', `url(${imageUrl})`);
                box.classList.add('hovered');
            });

            box.addEventListener('mouseleave', () => {
                box.classList.remove('hovered');
            });
        });

    }, []);

    useEffect(() => {
        if (initialRenderRef.current) {
            initialRenderRef.current = false;
            return;
        }

        if (animationInProgressRef.current) return;
        animationInProgressRef.current = true;

        const currentReviewItems = document.querySelectorAll(".review-item");
        if (currentReviewItems.length > 0) {
            if (!hasInitialClickRef.current) {
                hasInitialClickRef.current = true;
                const initialReviewCopy =
                    currentReviewItems[0].querySelector("#review-copy");
                const initialReviewAuthor =
                    currentReviewItems[0].querySelector("#review-author");

                if (initialReviewCopy && initialReviewAuthor) {
                    new SplitType(initialReviewCopy, {
                        types: "lines",
                        lineClass: "line",
                    });

                    new SplitType(initialReviewAuthor, {
                        types: "lines",
                        lineClass: "line",
                    });

                    initialReviewCopy.querySelectorAll(".line").forEach((line) => {
                        const content = line.innerHTML;
                        line.innerHTML = `<span>${content}</span>`;
                    });

                    initialReviewAuthor.querySelectorAll(".line").forEach((line) => {
                        const content = line.innerHTML;
                        line.innerHTML = `<span>${content}</span>`;
                    });
                }
            }

            const currentReview = currentReviewItems[currentReviewItems.length - 1];
            const lineSpans = currentReview.querySelectorAll(".line span");

            gsap.to(lineSpans, {
                yPercent: -110,
                duration: 0.7,
                stagger: 0.05,
                ease: "power4.in",
            });
        }

        const newReviewItem = document.createElement("div");
        newReviewItem.className = "review-item";

        newReviewItem.innerHTML = `
      <h4 id="review-copy">${reviews[activeReview].copy}</h4>
      <h4 id="review-author">- ${reviews[activeReview].author}</h4>
    `;

        if (reviewsContainerRef.current) {
            reviewsContainerRef.current.appendChild(newReviewItem);

            const newReviewCopy = newReviewItem.querySelector("#review-copy");
            const newReviewAuthor = newReviewItem.querySelector("#review-author");

            new SplitType(newReviewCopy, {
                types: "lines",
                lineClass: "line",
            });

            new SplitType(newReviewAuthor, {
                types: "lines",
                lineClass: "line",
            });

            const newLineSpans = [];

            newReviewCopy.querySelectorAll(".line").forEach((line) => {
                const content = line.innerHTML;
                line.innerHTML = `<span>${content}</span>`;
                newLineSpans.push(line.querySelector("span"));
            });

            newReviewAuthor.querySelectorAll(".line").forEach((line) => {
                const content = line.innerHTML;
                line.innerHTML = `<span>${content}</span>`;
                newLineSpans.push(line.querySelector("span"));
            });

            gsap.set(newLineSpans, { yPercent: 110 });

            gsap.to(newLineSpans, {
                yPercent: 0,
                duration: 0.7,
                stagger: 0.1,
                ease: "power4.out",
                delay: 0.7,
                onComplete: () => {
                    const reviewItems = document.querySelectorAll(".review-item");
                    if (reviewItems.length > 1) {
                        for (let i = 0; i < reviewItems.length - 1; i++) {
                            reviewItems[i].remove();
                        }
                    }
                    animationInProgressRef.current = false;
                },
            });
        }
    }, [activeReview]);

    const handleReviewClick = (index) => {
        if (index !== activeReview && !animationInProgressRef.current) {
            setActiveReview(index);
        }
    };

    return (
        <>
            {/* Trusted Hospitals Section */}
            <section className="trusted-hospitals" ref={trustedHospitalsRef}>
                <div className="trusted-hospitals-header">
                    <h2>✦ Trusted by Leading Hospitals</h2>
                </div>

                <div className="hospitals-grid">
                    {hospitalsData.map((hospital) => (
                        <div key={hospital.id} className="hospital" data-image={hospital.image}>
                            <h4 className="hospital-name">{hospital.name}</h4>
                        </div>
                    ))}
                </div>
            </section>

            {/* Reviews Section */}
            <section className="reviews" >
                <div className="review-box" ref={reviewsContainerRef}>

                    <h3 id="quote-icon">
                        <BiSolidQuoteLeft />
                    </h3>

                    <div className="review-item">
                        <h4 id="review-copy">{reviews[activeReview].copy}</h4>
                        <h4 id="review-author">- {reviews[activeReview].author}</h4>
                    </div>

                    <div className="reviews-list">
                        {reviews.map((review, index) => (
                            <div
                                key={review.id}
                                className={`review-thumbnail ${index === activeReview ? "active" : ""
                                    }`}
                                onClick={() => handleReviewClick(index)}
                            >
                                <img src={review.image} alt={`Review by ${review.author}`} />
                            </div>
                        ))}
                    </div>
                </div>
            </section>
        </>
    );
};

export default Reviews;
