/* Transition Overlays - Clean CSS file containing only transition-related styles */

/* Transition Overlay Styles */
.transition {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 100000;
    pointer-events: none;
}

.transition-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: scaleY(0);
    transform-origin: top;
    will-change: transform;
}

.transition-overlay.overlay-1 {
    background-color: #141414;
    /* Black */
}

.transition-overlay.overlay-2 {
    background-color: #5aabed;
    /* Red */
}

.transition-overlay.overlay-3 {
    background-color: #074a96;
    /* Yellow */
}

.transition-overlay.overlay-4 {
    background-color: #3575ff;
    /* Teal */
}

.transition-overlay.overlay-5 {
    background-color: #b3defa;
    /* Purple */
}