/* CSS Variables for your color scheme */
:root {
  /* CSS HEX */
  --night: #121214ff;
  --dim-gray: #727072ff;
  --baby-powder: #f7f7f2;

  /* CSS HSL */
  --night-hsl: hsla(240, 5%, 7%, 1);
  --dim-gray-hsl: hsla(300, 1%, 44%, 1);
  --baby-powder-hsl: hsla(60, 24%, 96%, 1);

  /* SCSS RGB */
  --night-rgb: rgba(18, 18, 20, 1);
  --dim-gray-rgb: rgba(114, 112, 114, 1);
  --baby-powder-rgb: rgba(247, 247, 242, 1);

  /* Gradients */
  --gradient-top: linear-gradient(0deg, #121214ff, #727072ff, #f7f7f2ff);
  --gradient-right: linear-gradient(90deg, #121214ff, #727072ff, #f7f7f2ff);
  --gradient-bottom: linear-gradient(180deg, #121214ff, #727072ff, #f7f7f2ff);
  --gradient-left: linear-gradient(270deg, #121214ff, #727072ff, #f7f7f2ff);
  --gradient-top-right: linear-gradient(45deg, #121214ff, #727072ff, #f7f7f2ff);
  --gradient-bottom-right: linear-gradient(135deg, #121214ff, #727072ff, #f7f7f2ff);
  --gradient-top-left: linear-gradient(225deg, #121214ff, #727072ff, #f7f7f2ff);
  --gradient-bottom-left: linear-gradient(315deg, #121214ff, #727072ff, #f7f7f2ff);
  --gradient-radial: radial-gradient(#121214ff, #727072ff, #f7f7f2ff);
}

.home-page {
  width: 100vw;
  min-height: 100vh;
  background: transparent;
  position: relative;
  /* Changed to relative for positioning children */
  z-index: 1;
}

.home-header {
  position: absolute;
  top: 120px;
  /* Position below navbar */
  left: 40px;
}

.home-center-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}

.home-header h1 {
  font-size: 5rem;
  margin-bottom: 1rem;
  font-family: 'Rader', sans-serif;
  font-weight: 700;
  color: var(--baby-powder);
}

.home-center-content p {
  font-size: 1.2rem;
  color: var(--dim-gray);
  font-family: 'Rader', sans-serif;
  font-weight: 400;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .home-header {
    top: 100px;
    left: 20px;
  }

  .home-header h1 {
    font-size: 2rem;
  }

  .home-center-content p {
    font-size: 1rem;
  }
}