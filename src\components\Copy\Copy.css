/* Copy Component Styles for Text Reveal Animations */

/* Ensure proper overflow for line masking */
.line {
  overflow: hidden;
  position: relative;
}

/* Line class generated by SplitText with auto-incrementing numbers */
[class*="line"] {
  overflow: hidden;
  position: relative;
}

/* Ensure text elements have proper overflow for animations */
[data-copy-wrapper="true"]>*,
[data-copy-wrapper="true"] {
  overflow: hidden;
}

/* Additional styling for better text reveal effect */
.copy-container {
  overflow: hidden;
}

.copy-container * {
  overflow: hidden;
}