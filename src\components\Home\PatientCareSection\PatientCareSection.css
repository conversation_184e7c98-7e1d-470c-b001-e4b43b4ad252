/* Patient Care Section Styles */
.patient-care-header {
  position: relative;
  width: 100vw;
  height: 100vh;
  min-height: 600px;
  padding: 2em;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: transparent;
  color: var(--baby-powder, #f7f7f2);
  box-sizing: border-box;
}

.patient-care-header .patient-care-profile-icon {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 1em;
  margin-bottom: 1em;
  outline: 0.25rem solid var(--dim-gray, #727072);
  border: 0.25rem solid var(--baby-powder, #f7f7f2);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000000;
}

.patient-care-header .healthcare-icon {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.patient-care-header .healthcare-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.patient-care-header .healthcare-icon:has(.logo-image)::before {
  opacity: 1;
}

.patient-care-header .healthcare-icon span {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--baby-powder, #f7f7f2);
}

.patient-care-header .healthcare-icon .logo-image {
  position: relative;
  width: 120%;
  height: 140%;
  object-fit: contain;
  z-index: 0;
}

.patient-care-header .patient-care-header-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1em;
}

.patient-care-header .patient-care-title {
  margin-bottom: 6em;
}

.patient-care-header .patient-care-footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  padding: 2em;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  box-sizing: border-box;
}

.patient-care-header .patient-care-footer .patient-care-footer-scroll-down {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.patient-care-header .patient-care-footer .patient-care-footer-symbols {
  height: auto;
}

.patient-care-header .trust-indicators {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}



.patient-care-header .trust-badge {
  padding: 0.6rem 1.2rem;
  background-color: var(--night);
  color: var(--baby-powder);
  border-radius: 0.3rem;
  font-size: 0.9rem;
  font-weight: 600;
  font-family: 'Rader', sans-serif;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.patient-care-header-content p,
.patient-care-title h1 {
  line-height: 1.1;
  font-family: 'Rader', sans-serif;
}

.patient-care-header-content p {
  font-size: 1.2rem;
  color: var(--dim-gray);
  font-weight: 400;
}

.patient-care-title h1 {
  font-size: 5.5rem;
  font-weight: 700;
  color: var(--night);
  margin: 0;
  font-style: italic;
  letter-spacing: -1px;
  line-height: 1;
}

.patient-care-arrow-icon h1 {
  font-size: 3rem;
  color: var(--night);
  margin: 0;
  animation: bounce 2s infinite;
  transform: rotate(-15deg);
  font-weight: 900;
  font-style: italic;
}

.patient-care-footer p {
  font-family: 'Rader', sans-serif;
  font-size: 1rem;
  color: var(--night);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

/* Copy Component Integration Styles */
.patient-care-header-content p>div,
.patient-care-title h1>div {
  position: relative;
  padding: 0 0.25em;
}

/* Ensure proper overflow for Copy component text animations */
.patient-care-header-content p,
.patient-care-footer p {
  overflow: hidden;
}

.patient-care-title h1 {
  overflow: visible;
}

/* Initial state for animated elements */
.patient-care-profile-icon,
.patient-care-arrow-icon,
.trust-indicators .trust-badge {
  opacity: 0;
}



/* Bounce animation for arrow */
@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

/* Mobile Responsive Styles */
@media (max-width: 1200px) {
  .patient-care-title h1 {
    font-size: 4.5rem;
    line-height: 1;
  }

  .patient-care-header {
    padding: 1.5em;
  }

  .patient-care-header .patient-care-footer {
    padding: 1.5em;
  }
}

@media (max-width: 1000px) {
  .patient-care-header .patient-care-footer .patient-care-footer-symbols {
    display: none;
  }

  .patient-care-header .patient-care-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1.2em;
  }

  .patient-care-header .patient-care-footer .patient-care-footer-scroll-down {
    left: 1.2em;
    transform: translateX(0%);
  }

  .patient-care-title h1 {
    font-size: 3.5rem;
    line-height: 0.95;
  }

  .patient-care-header .patient-care-profile-icon {
    width: 80px;
    height: 80px;
  }

  .patient-care-header .healthcare-icon span {
    font-size: 2rem;
  }

  .patient-care-header .trust-indicators {
    flex-direction: row;
    gap: 0.5rem;
  }

  .patient-care-header .trust-badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
  }

  .patient-care-header .patient-care-title {
    margin-bottom: 4em;
  }
}

@media (max-width: 768px) {
  .patient-care-header {
    padding: 1em;
    height: 100vh;
  }

  .patient-care-header .patient-care-footer {
    padding: 1em;
  }

  .patient-care-header .patient-care-footer .patient-care-footer-scroll-down {
    left: 1em;
  }

  .patient-care-title h1 {
    font-size: 2.8rem;
    line-height: 0.9;
    letter-spacing: -0.5px;
  }

  .patient-care-header-content p {
    font-size: 1rem;
  }

  .patient-care-header .patient-care-profile-icon {
    width: 70px;
    height: 70px;
    margin-bottom: 0.8em;
  }

  .patient-care-header .healthcare-icon span {
    font-size: 1.5rem;
  }

  .patient-care-header .patient-care-title {
    margin-bottom: 3em;
  }

  .patient-care-arrow-icon h1 {
    font-size: 2.5rem;
  }

  .patient-care-footer p {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .patient-care-header {
    padding: 0.8em;
  }

  .patient-care-header .patient-care-footer {
    padding: 0.8em;
  }

  .patient-care-header .patient-care-footer .patient-care-footer-scroll-down {
    left: 0.8em;
  }

  .patient-care-title h1 {
    font-size: 2.2rem;
    line-height: 0.85;
    letter-spacing: -0.3px;
  }

  .patient-care-header-content p {
    font-size: 0.9rem;
  }

  .patient-care-header .patient-care-profile-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 0.6em;
  }

  .patient-care-header .healthcare-icon span {
    font-size: 1.2rem;
  }

  .patient-care-header .patient-care-title {
    margin-bottom: 2.5em;
  }

  .patient-care-arrow-icon h1 {
    font-size: 2rem;
  }

  .patient-care-footer p {
    font-size: 0.8rem;
    letter-spacing: 0.5px;
  }

  .patient-care-header .patient-care-header-content {
    gap: 0.8em;
  }
}

@media (max-width: 360px) {
  .patient-care-title h1 {
    font-size: 1.8rem;
    line-height: 0.8;
  }

  .patient-care-header-content p {
    font-size: 0.8rem;
  }

  .patient-care-header .patient-care-profile-icon {
    width: 50px;
    height: 50px;
  }

  .patient-care-header .healthcare-icon span {
    font-size: 1rem;
  }

  .patient-care-arrow-icon h1 {
    font-size: 1.5rem;
  }

  .patient-care-footer p {
    font-size: 0.7rem;
  }
}