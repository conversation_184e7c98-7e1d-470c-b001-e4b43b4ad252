import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import './DigitCounter.css';

const DigitCounter = ({
  targetNumber = 100,
  duration = 2,
  delay = 0,
  parallaxSpeed = 0.5
}) => {
  const counterRef = useRef(null);
  const digitRefs = useRef([]);
  const [currentNumber, setCurrentNumber] = useState(0);

  // Get number of digits needed for target number
  const getDigitCount = (num) => {
    return num.toString().length;
  };

  // Convert number to individual digits without padding
  const numberToDigits = (num) => {
    return num.toString().split('').map(Number);
  };

  const digits = numberToDigits(currentNumber);
  const digitCount = getDigitCount(targetNumber);

  useEffect(() => {
    // Create digit columns with numbers 0-9
    digitRefs.current.forEach((digitRef, columnIndex) => {
      if (digitRef) {
        // Clear existing content
        digitRef.innerHTML = '';

        // Create digit strip for this column
        for (let i = 0; i <= 9; i++) {
          const digitElement = document.createElement('div');
          digitElement.className = 'digit-item';
          digitElement.textContent = i;
          digitRef.appendChild(digitElement);
        }
      }
    });
  }, [digitCount]);

  useEffect(() => {
    const timeline = gsap.timeline({ delay });

    // Animate each digit column
    timeline.to({ value: 0 }, {
      value: targetNumber,
      duration,
      ease: "power2.out",
      onUpdate: function () {
        const currentVal = Math.floor(this.targets()[0].value);
        setCurrentNumber(currentVal);

        // Animate each digit column
        const currentDigits = numberToDigits(currentVal);

        digitRefs.current.forEach((digitRef, columnIndex) => {
          if (digitRef && columnIndex < currentDigits.length) {
            const targetDigit = currentDigits[currentDigits.length - 1 - columnIndex];
            // Check if mobile view
            const isMobile = window.innerWidth <= 768;
            const digitHeight = isMobile ? 45 : 60;
            const yPosition = -targetDigit * digitHeight;

            gsap.set(digitRef, {
              y: yPosition
            });
          }
        });
      }
    });

    // Parallax effect on scroll
    const handleScroll = () => {
      const scrollY = window.scrollY;
      if (counterRef.current) {
        gsap.set(counterRef.current, {
          y: scrollY * parallaxSpeed
        });
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      timeline.kill();
      window.removeEventListener('scroll', handleScroll);
    };
  }, [targetNumber, duration, delay, parallaxSpeed]);

  return (
    <div ref={counterRef} className="digit-counter">
      <div className="counter-container">
        {Array.from({ length: digitCount }, (_, index) => (
          <div key={index} className="digit-wrapper">
            <div
              ref={el => digitRefs.current[digitCount - 1 - index] = el}
              className="digit-strip"
            />
          </div>
        ))}
        <span className="counter-suffix">%</span>
      </div>
    </div>
  );
};

export default DigitCounter;
